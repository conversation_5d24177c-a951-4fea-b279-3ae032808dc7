#!/usr/bin/env python3
"""
Test email connection to verify credentials work
"""

import imaplib
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_email_connection():
    """Test IMAP connection with hardcoded credentials"""
    
    config = {
        'IMAP_SERVER': 'imap.dreamhost.com',
        'IMAP_PORT': 993,
        'IMAP_USERNAME': 'da<PERSON>@dualtrace.us',
        'IMAP_PASSWORD': 'lifecycleevent02025!?',
    }
    
    logger.info("Testing email connection...")
    logger.info(f"Server: {config['IMAP_SERVER']}:{config['IMAP_PORT']}")
    logger.info(f"Username: {config['IMAP_USERNAME']}")
    
    try:
        # Connect to IMAP server
        mail_server = imaplib.IMAP4_SSL(config['IMAP_SERVER'], config['IMAP_PORT'])
        logger.info("✓ Connected to IMAP server")
        
        # Login
        mail_server.login(config['IMAP_USERNAME'], config['IMAP_PASSWORD'])
        logger.info("✓ Authentication successful")
        
        # List folders
        status, folders = mail_server.list()
        if status == 'OK':
            logger.info("✓ Folder listing successful")
            logger.info("Available folders:")
            for folder in folders[:5]:  # Show first 5 folders
                logger.info(f"  {folder}")
        
        # Select INBOX
        status, messages = mail_server.select('INBOX')
        if status == 'OK':
            logger.info(f"✓ INBOX selected - {messages[0].decode()} messages")
        
        # Logout
        mail_server.logout()
        logger.info("✓ Connection closed successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Email connection failed: {e}")
        return False

if __name__ == "__main__":
    success = test_email_connection()
    if success:
        print("\n🎉 Email connection test PASSED!")
    else:
        print("\n❌ Email connection test FAILED!")
